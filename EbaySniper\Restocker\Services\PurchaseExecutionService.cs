﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using uBuyFirst.Data;
using uBuyFirst.Purchasing;
using uBuyFirst.Restocker.Data;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.Restocker.Services
{
    /// <summary>
    /// Service responsible for executing purchase attempts through the Restocker system.
    ///
    /// CRITICAL BUSINESS RULE: This service enforces over-purchase prevention by ensuring that
    /// PurchasedQuantity never exceeds RequiredQuantity for any JobId. This prevents budget
    /// overruns and ensures precise quantity control in the Restocker module.
    ///
    /// The service automatically stops purchasing when the required quantity is reached and
    /// calculates exact quantities needed to prevent over-purchasing.
    /// </summary>
    public class PurchaseExecutionService : IDisposable
    {
        private readonly IPurchaseTrackerRepository _repository;
        private bool _disposed = false;

        public PurchaseExecutionService(IPurchaseTrackerRepository repository)
        {
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        }

        /// <summary>
        /// Attempts to purchase an item for a specific keyword's restock requirements
        /// </summary>
        /// <param name="dataList">The eBay item to potentially purchase</param>
        /// <param name="keyword">The keyword with restock requirements</param>
        /// <param name="filterAlias">The filter alias that triggered this purchase attempt</param>
        /// <returns>Result of the purchase attempt</returns>
        public async Task<PurchaseExecutionResult> TryPurchaseItemAsync(DataList dataList, Keyword2Find keyword, string filterAlias)
        {
            if (dataList == null)
                throw new ArgumentNullException(nameof(dataList));
            if (keyword == null)
                throw new ArgumentNullException(nameof(keyword));

            try
            {
                // Check if keyword has restock configuration
                if (string.IsNullOrEmpty(keyword.JobId) || keyword.RequiredQuantity <= 0)
                {
                    return PurchaseExecutionResult.CreateSkipped("Keyword has no restock configuration");
                }

                // OVER-PURCHASE PREVENTION: Check if we still need to purchase items
                if (keyword.PurchasedQuantity >= keyword.RequiredQuantity)
                {
                    return PurchaseExecutionResult.CreateSkipped("Required quantity already reached");
                }

                // Process the purchase for this keyword
                var result = await ProcessKeywordPurchaseAsync(dataList, keyword, filterAlias);
                return result;
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error during purchase execution for item {dataList.ItemID}: {ex.Message}";
                await LogPurchaseAttemptAsync(keyword?.Id ?? "", keyword?.JobId ?? "", dataList.ItemID, "Failed", errorMessage);
                return PurchaseExecutionResult.CreateFailure(errorMessage, ex);
            }
        }

        /// <summary>
        /// Processes a purchase attempt for a specific keyword
        /// </summary>
        private async Task<PurchaseExecutionResult> ProcessKeywordPurchaseAsync(
            DataList dataList, Keyword2Find keyword, string filterAlias)
        {
            try
            {
                // Log the purchase attempt
                await LogPurchaseAttemptAsync(keyword.Id, keyword.JobId, dataList.ItemID, "Attempting", null).ConfigureAwait(false);

                // Check if credit card checkout is available
                if (!CreditCardService.CreditCardPaymentEnabled)
                {
                    var message = "Credit card payment is not enabled";
                    await LogPurchaseAttemptAsync(keyword.Id, keyword.JobId, dataList.ItemID, "Skipped", message).ConfigureAwait(false);
                    return PurchaseExecutionResult.CreateSkipped(message);
                }

                // Validate item availability
                if (dataList.QuantityAvailable <= 0)
                {
                    var message = "Item is not available for purchase";
                    await LogPurchaseAttemptAsync(keyword.Id, keyword.JobId, dataList.ItemID, "Skipped", message).ConfigureAwait(false);
                    return PurchaseExecutionResult.CreateSkipped(message);
                }

                // OVER-PURCHASE PREVENTION: Calculate exact quantity to purchase
                // This ensures we never purchase more than required (PurchasedQuantity <= RequiredQuantity)
                var remainingQuantity = keyword.RequiredQuantity - keyword.PurchasedQuantity;
                var quantityToPurchase = Math.Min(remainingQuantity, dataList.QuantityAvailable);

                // Double-check: If no quantity needed, skip the purchase
                if (quantityToPurchase <= 0)
                {
                    var message = "Purchase requirement already fulfilled";
                    await LogPurchaseAttemptAsync(keyword.Id, keyword.JobId, dataList.ItemID, "Skipped", message);
                    return PurchaseExecutionResult.CreateSkipped(message);
                }

                // Execute the purchase
                var purchaseResult = await ExecutePurchaseAsync(dataList, quantityToPurchase, keyword, filterAlias);

                return purchaseResult;
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error processing purchase for keyword {keyword.Id}: {ex.Message}";
                await LogPurchaseAttemptAsync(keyword.Id, keyword.JobId, dataList.ItemID, "Failed", errorMessage);
                return PurchaseExecutionResult.CreateFailure(errorMessage, ex);
            }
        }

        /// <summary>
        /// Executes the actual purchase using the existing CreditCardCheckout system
        /// </summary>
        private async Task<PurchaseExecutionResult> ExecutePurchaseAsync(DataList dataList, int quantity, Keyword2Find keyword, string filterAlias)
        {
            try
            {
                // Execute the credit card checkout with the calculated quantity
                await CreditCardCheckout.ExecuteCreditCardCheckout(dataList, quantity).ConfigureAwait(false);

                // Monitor the purchase status

                if (dataList.Order?.CheckoutStatus == BuyingService.Order.CheckoutState.PaymentSuccess
                    || dataList.Order?.CheckoutStatus == BuyingService.Order.CheckoutState.TestPurchase)
                {
                    // Record successful transaction and update keyword
                    await RecordSuccessfulTransactionAsync(keyword, dataList, quantity, filterAlias);
                    await LogPurchaseAttemptAsync(keyword.Id, keyword.JobId, dataList.ItemID, "Success", null);

                    // Update keyword's purchased quantity
                    keyword.PurchasedQuantity += quantity;

                    // Save settings to persist the updated purchase quantity
                    Form1.Instance?.SaveSettings();

                    return PurchaseExecutionResult.CreateSuccess(
                        $"Successfully purchased {quantity} of item {dataList.ItemID} for job {keyword.JobId}",
                        quantity);
                }
                else
                {
                    var errorMessage = dataList.Order?.FailureReasonMessage ?? "Purchase failed for unknown reason";
                    await LogPurchaseAttemptAsync(keyword.Id, keyword.JobId, dataList.ItemID, "Failed", errorMessage);
                    return PurchaseExecutionResult.CreateFailure(errorMessage);
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error executing purchase: {ex.Message}";
                await LogPurchaseAttemptAsync(keyword.Id, keyword.JobId, dataList.ItemID, "Failed", errorMessage);
                return PurchaseExecutionResult.CreateFailure(errorMessage, ex);
            }
        }


        /// <summary>
        /// Records a successful transaction in the database
        /// </summary>
        private async Task RecordSuccessfulTransactionAsync(
            Keyword2Find keyword, DataList dataList, int quantity, string filterAlias)
        {
            var transaction = new PurchaseTransaction
            {
                KeywordId = keyword.Id,
                JobId = keyword.JobId,
                ItemId = dataList.ItemID,
                ItemTitle = dataList.Title,
                PurchasePrice = (decimal)(dataList.ItemPricing?.GetEffectivePurchasePrice()?.Value ?? 0),
                Quantity = quantity,
                Status = "Completed",
                TransactionId = dataList.Order?.SessionID,
                PaymentMethod = "Credit Card",
                Notes = $"Purchased via Restock filter: {filterAlias}"
            };

            await _repository.AddTransactionAsync(transaction);
        }

        /// <summary>
        /// Logs a purchase attempt to the database
        /// </summary>
        private async Task LogPurchaseAttemptAsync(string keywordId, string jobId, string itemId, string result, string errorMessage)
        {
            var attempt = new PurchaseAttempt
            {
                KeywordId = keywordId,
                JobId = jobId,
                ItemId = itemId,
                Result = result,
                ErrorMessage = errorMessage
            };

            await _repository.AddAttemptAsync(attempt).ConfigureAwait(false);
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _repository?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Result of a purchase execution attempt
    /// </summary>
    public class PurchaseExecutionResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public Exception Exception { get; set; }
        public int QuantityPurchased { get; set; }

        public static PurchaseExecutionResult CreateSuccess(string message, int quantityPurchased = 0)
        {
            return new PurchaseExecutionResult
            {
                Success = true,
                Message = message,
                QuantityPurchased = quantityPurchased
            };
        }

        public static PurchaseExecutionResult CreateFailure(string message, Exception exception = null)
        {
            return new PurchaseExecutionResult
            {
                Success = false,
                Message = message,
                Exception = exception
            };
        }

        public static PurchaseExecutionResult CreateSkipped(string message)
        {
            return new PurchaseExecutionResult
            {
                Success = false,
                Message = message
            };
        }
    }
}
